"use client";

import { useEffect, useState } from "react";
import { getProviders, signIn } from "next-auth/react";
import { FaGithub } from "react-icons/fa";
import Link from "next/link";
import { AppButton } from "@/app/components/app-button";
import { AppTypography } from "@/app/components/app-typography";

interface Provider {
  id: string;
  name: string;
}

export default function SignIn() {
  const [providers, setProviders] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProviders = async () => {
      const res = await getProviders();
      setProviders(res);
      setIsLoading(false);
    };

    fetchProviders();
  }, []);

  return (
    <div className="main-container w-full overflow-x-hidden">
      {isLoading ? (
        <div className="flex h-screen items-center justify-center">
          <div className="loader text-white">Loading ...</div>
        </div>
      ) : (
        <div
          className="flex min-h-screen items-center justify-center py-8"
          style={{
            backgroundImage: `url('/main-bg.jpg')`,
            backgroundBlendMode: "color, normal",
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <div className="w-full max-w-[720px] rounded-[20px] border border-white/10 bg-[#1A1F23] px-[48px] py-[40px]">
            <div className="mb-10 flex flex-col items-center gap-1">
              <AppTypography
                variant="highlight-1"
                className="text-center text-[72px] font-bold leading-[0.89] text-[#DFDBFA]"
              >
                Welcome to Recon
              </AppTypography>
            </div>

            <div className="mb-10 flex flex-col gap-3">
              <AppTypography
                variant="title-3-strong"
                className="text-[20px] font-bold leading-[1.3] text-white/80"
              >
                Log in
              </AppTypography>
              {providers &&
                Object.values(providers).map((provider: Provider) => (
                  <AppButton
                    key={provider.name}
                    variant="primary"
                    size="lg"
                    fullWidth
                    onClick={() =>
                      signIn(provider.id, { callbackUrl: "/dashboard" })
                    }
                    leftIcon={<FaGithub size={24} />}
                    className="items-center justify-center gap-1 rounded-lg border-[#DFDBFA] bg-[#DFDBFA] px-3 py-2 text-[24px] font-bold leading-[0.83] text-[#5649B0] hover:bg-[#DFDBFA]/90"
                  >
                    Sign in with Github
                  </AppButton>
                ))}
            </div>
            <div className="mb-10 flex flex-col gap-3">
              <AppTypography
                variant="title-3-strong"
                className="text-[20px] font-bold leading-[1.3] text-white/80"
              >
                Try with Recon with no login
              </AppTypography>
              <Link href="/tools/sandbox">
                <AppButton
                  variant="outline"
                  size="lg"
                  fullWidth
                  className="items-center justify-center rounded-lg border-[#DFDBFA] bg-[#7160E8] px-3 py-2 text-[24px] font-bold leading-[0.83] text-[#DFDBFA] hover:bg-[#7160E8]/90"
                >
                  Scafofold invariants Sanbox
                </AppButton>
              </Link>
            </div>

            {/* Scrappers Section */}
            <div className="flex flex-col gap-3">
              <AppTypography
                variant="title-3-strong"
                className="text-[20px] font-bold leading-[1.3] text-white/80"
              >
                Scrappers
              </AppTypography>
              <Link href="/tools/medusa">
                <AppButton
                  variant="outline"
                  size="lg"
                  fullWidth
                  className="mb-3 items-center justify-center rounded-lg border-[#DFDBFA] bg-[#7160E8] px-3 py-2 text-[24px] font-bold leading-[0.83] text-[#DFDBFA] hover:bg-[#7160E8]/90"
                >
                  Medusa Log
                </AppButton>
              </Link>
              <Link href="/tools/echidna">
                <AppButton
                  variant="outline"
                  size="lg"
                  fullWidth
                  className="items-center justify-center rounded-lg border-[#DFDBFA] bg-[#7160E8] px-3 py-2 text-[24px] font-bold leading-[0.83] text-[#DFDBFA] hover:bg-[#7160E8]/90"
                >
                  Echidna Log
                </AppButton>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
