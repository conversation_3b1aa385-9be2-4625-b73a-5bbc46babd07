import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { <PERSON>a<PERSON><PERSON>, <PERSON>aD<PERSON>rd, <PERSON>a<PERSON>oon, <PERSON>a<PERSON>un } from "react-icons/fa";

import { useGetDiscord } from "@/app/services/discord.hook.ts";
import { OrgStatus, useGetMyOrg } from "@/app/services/organization.hooks";
// import { useGetInstalls } from "@/app/services/installs.hook";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { AppButton } from "../app-button";
import { AppLogo } from "../app-logo";
import { AppSwitch } from "../app-switch";
import { DashboardUser } from "./dashboard-user";
import { SidebarItem } from "./sidebar-item";
import { useGetSidebarLinks } from "./use-get-sidebar-links";

export const DashboardNavbar = () => {
  // NOTE: This ensure we preload installs
  const { items, isChildItemsLoading } = useGetSidebarLinks();
  const { data: organization, isLoading, orgStatus } = useGetMyOrg();
  const { setTheme, theme: currentTheme } = useTheme();
  const [isDark, setIsDark] = useState(false);

  const { data: discordUrl } = useGetDiscord();
  const pathname = usePathname();

  const hideSidebar =
    pathname.includes("/shares/") || pathname.includes("/tools/medusa");

  const onThemeChange = (darkEnabled: boolean) => {
    setTheme(darkEnabled ? THEME_OPTIONS.dark : THEME_OPTIONS.light);
  };

  const ThemeIcon = isDark ? FaSun : FaMoon;

  useEffect(() => {
    // This will only run on the client-side
    setIsDark(currentTheme === THEME_OPTIONS.dark);
  }, [currentTheme]);

  if (hideSidebar) return null;

  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname
    <aside className="navbar-side aside-menu bg-aside min-w-[300px] max-w-[300px] overflow-y-auto pb-[27px]">
      <Link
        href="/dashboard"
        className="block cursor-pointer pl-[29px] pt-[33px]"
      >
        <AppLogo />
      </Link>
      <ul className="mt-[24px]">
        {items.map((item) => (
          <SidebarItem
            key={item.label}
            {...item}
            isChildItemsLoading={isChildItemsLoading}
          />
        ))}
      </ul>
      <a
        href={discordUrl}
        target="_blank"
        rel="nofollow noreferrer"
        className="cursor-pointer"
      >
        <div className="mb-[55px] ml-[28px] mt-[24px] flex gap-[18px] text-[16px] leading-[19px] text-textPrimary">
          <FaDiscord className="h-[16px] w-[20px]" />
          Discord
        </div>
      </a>
      <a
        href={"https://book.getrecon.xyz/"}
        target="_blank"
        rel="nofollow noreferrer"
        className="cursor-pointer"
      >
        <div className="mb-[55px] ml-[28px] mt-[24px] flex gap-[18px] text-[16px] leading-[19px] text-textPrimary">
          <FaBook className="h-[16px] w-[20px]" />
          Tutorials
        </div>
      </a>

      {orgStatus === OrgStatus.FREE && !isLoading && (
        <div className="gradient-dark-bg relative mx-[18px] mb-[20px] w-[268px] self-center rounded-[16px] bg-blockBg py-[36px] pl-[26px] pr-[50px]">
          <div className="relative z-20">
            <p className="mb-[8px] text-[18px] font-bold uppercase leading-[21px] text-textPrimary">
              Upgrade to PRO!
            </p>
            <p className="mb-[12px] text-[17px] text-textPrimary">
              Run invariant tests in the cloud, in less than 1 minute
            </p>
          </div>
          <Link href="/dashboard/pro">
            <Image
              src="/upgrade-to-pro-bg.svg"
              alt="Recon Logo"
              width={154}
              height={154}
              className="absolute right-[44px] top-[17px] z-10"
            />
            <AppButton
              variant="primary"
              className="mt-[12px] w-[121px] px-[14px] py-[9px]"
            >
              Go Pro &gt;
            </AppButton>
          </Link>
        </div>
      )}

      <div className="px-[18px]">
        <div className="mb-[49px] mt-auto flex w-[100%] items-center  justify-between">
          <ThemeIcon className="mr-[18px] text-[#CFCFCF]" />
          <span className="text-[16px] leading-[19px] text-textSecondary">
            Switch to {isDark ? "Light" : "Dark"} theme
          </span>
          <AppSwitch
            className="ml-auto"
            onChange={onThemeChange}
            enabled={isDark}
          />
        </div>

        <div className="gradient-dark-bg flex w-[100%] items-center gap-[15px] self-center rounded-[8px] bg-blockBg p-[12px]">
          <DashboardUser
            {...{
              organization,
              isLoading,
              orgStatus,
            }}
          />
        </div>
      </div>
    </aside>
  );
};
