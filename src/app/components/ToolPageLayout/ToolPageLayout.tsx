"use client";
import Link from "next/link";
import { useState } from "react";
import { FiArrowLeft, FiPlay, FiX } from "react-icons/fi";

import type { ENV_TYPE } from "@/app/app.constants";
import { AppInput } from "@/app/components/app-input";
import { AppLogo } from "@/app/components/app-logo";

import { AppTextarea } from "@/app/components/app-textarea";
import LogComponent from "@/app/components/LogComponent/LogComponent";
import VideoPlayer from "@/app/components/VideoPlayer/VideoPlayer";

interface ToolPageLayoutProps {
  toolType: ENV_TYPE;
  toolName: string;
  toolDescription: string[];
  youtubeUrl?: string;
  youtubeOverlayText?: string;
}

export default function ToolPageLayout({
  toolType,
  toolName,
  toolDescription,
  youtubeUrl,
  youtubeOverlayText,
}: ToolPageLayoutProps) {
  const [logs, setLogs] = useState("");
  const [prefix, setPrefix] = useState("");
  const [isVideoVisible, setIsVideoVisible] = useState(false);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
          <Link href="/dashboard" className="cursor-pointer">
            <AppLogo />
          </Link>
        </div>

        <div
          className="flex min-h-[calc(100vh-80px)] items-center justify-center  py-8"
          style={{
            backgroundImage: `url('/main-bg.jpg')`,
            backgroundBlendMode: "color, normal",
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <div className="w-full max-w-4xl rounded-[20px] bg-[#1A1F23] px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/"
                className="mb-6 inline-flex items-center text-white transition-colors duration-200 hover:text-gray-300"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <h1 className="main-title-custom mb-6  text-[40px] font-bold leading-[44px] tracking-normal lg:text-[56px] lg:leading-[60px]">
                {toolName}
              </h1>

              {toolDescription.map((desc, index) => (
                <p
                  key={`desc-${desc.slice(0, 20)}-${index}`}
                  className=" text-[18px] font-thin leading-[24px] text-white lg:text-[22px] lg:leading-[28px]"
                >
                  {desc}
                </p>
              ))}
            </div>

            {youtubeUrl && (
              <div className="mb-8">
                <button
                  onClick={() => setIsVideoVisible(!isVideoVisible)}
                  className="hover:bg-primary/80 mb-4 inline-flex items-center rounded-lg bg-primary px-4 py-2 text-white transition-all duration-200 hover:scale-105"
                >
                  {isVideoVisible ? (
                    <>
                      <FiX className="mr-2 size-4" />
                      Hide Tutorial Video
                    </>
                  ) : (
                    <>
                      <FiPlay className="mr-2 size-4" />
                      Show Tutorial Video
                    </>
                  )}
                </button>

                <div
                  className={`overflow-hidden transition-all duration-700 ease-in-out ${
                    isVideoVisible
                      ? "max-h-[600px] translate-y-0 scale-100 opacity-100"
                      : "max-h-0 -translate-y-8 scale-95 opacity-0"
                  }`}
                >
                  <div
                    className={`transition-all duration-500 ${
                      isVideoVisible ? "transform-none" : "scale-95"
                    }`}
                  >
                    <VideoPlayer
                      link={youtubeUrl}
                      overlayText={youtubeOverlayText}
                    />
                  </div>
                </div>
              </div>
            )}

            <AppInput
              className="mb-6"
              label="Add a Prefix for your convenience"
              value={prefix}
              onChange={(e) => setPrefix(e.target.value)}
              type="text"
            />

            <AppTextarea
              className="mb-6"
              label={`Paste ${toolName.split(" ")[0]} Logs Here`}
              value={logs}
              onChange={(e) => setLogs(e.target.value)}
              type="text"
            />

            <LogComponent fuzzer={toolType} logs={logs} prefix={prefix} />
          </div>
        </div>
      </div>
    </div>
  );
}
